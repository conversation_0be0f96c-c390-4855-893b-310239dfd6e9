apiVersion: v1
kind: Service
metadata:
  name: {{ include "agent-wrapper.fullname" . }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  labels:
    {{- include "agent-wrapper.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- if .Values.service.ports }}
    {{- with .Values.service.ports }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- else }}
    - port: {{ .Values.service.port }}
      targetPort: management
      protocol: TCP
      name: http
    {{- end }}
  selector:
    {{- include "agent-wrapper.selectorLabels" . | nindent 4 }}
