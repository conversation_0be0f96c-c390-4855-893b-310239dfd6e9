{{- if .Values.serviceAccount }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.serviceAccount.name | default (include "agent-wrapper.fullname" .) }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "agent-wrapper.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.automountServiceAccountToken }}
{{- end }}
