{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "agent-wrapper.fullname" . }}
  labels:
    {{- include "agent-wrapper.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "agent-wrapper.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
  {{- if .Values.autoscaling.behavior }}
  behavior:
    {{- if .Values.autoscaling.behavior.scaleUp }}
    scaleUp:
      {{- if .Values.autoscaling.behavior.scaleUp.stabilizationWindowSeconds }}
      stabilizationWindowSeconds: {{ .Values.autoscaling.behavior.scaleUp.stabilizationWindowSeconds }}
      {{- end }}
      {{- if .Values.autoscaling.behavior.scaleUp.policies }}
      policies:
        {{- toYaml .Values.autoscaling.behavior.scaleUp.policies | nindent 8 }}
      {{- end }}
    {{- end }}
    {{- if .Values.autoscaling.behavior.scaleDown }}
    scaleDown:
      {{- if .Values.autoscaling.behavior.scaleDown.stabilizationWindowSeconds }}
      stabilizationWindowSeconds: {{ .Values.autoscaling.behavior.scaleDown.stabilizationWindowSeconds }}
      {{- end }}
      {{- if .Values.autoscaling.behavior.scaleDown.policies }}
      policies:
        {{- toYaml .Values.autoscaling.behavior.scaleDown.policies | nindent 8 }}
      {{- end }}
    {{- end }}
  {{- end }}
{{- end }}
